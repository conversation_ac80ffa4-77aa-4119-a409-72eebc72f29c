import Link from "next/link";
import type { Metadata } from "next";
import Footer from "../../components/Footer/Footer";
import { AppButton } from "../../components/app-button";
import { LPGradientBackground } from "../../components/gradient-wrapper";
import PromoNavbar from "../lp-components/PromoNavbar";
import Testimonials from "../lp-components/Testimonials/Testimonials";
import BenefitsExtension from "../lp-components/BenefitsExtension/BenefitsExtension";
import CompanyLogos from "../lp-components/company-logos";
import { ReconUpgradeLogo } from "../../components/recon-upgrade-logo";
import { H1, Body4 } from "../../components/app-typography";

export const metadata: Metadata = {
  title: "Recon Extension - One Click Invariant Testing",
  description:
    "Scaffold and run Invariant Tests with Echidna, Medusa and Halmos, directly from VS Code. Trusted by teams at Centrifuge, Credit Coop, and more.",
};

interface FeatureItemProps {
  title: string;
  description?: string;
  highlighted?: boolean;
}

function FeatureItem({
  title,
  description,
  highlighted = false,
}: FeatureItemProps) {
  return (
    <div className="flex flex-col gap-1">
      <div className="flex items-center gap-3">
        {/* Checkmark Icon */}
        <div className="flex size-6 items-center justify-center rounded-full bg-[#7160E8]">
          <svg
            width="12"
            height="9"
            viewBox="0 0 12 9"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M1 4.5L4.5 8L11 1.5"
              stroke="white"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>

        {/* Title */}
        <Body4
          className={`text-[20px] font-bold leading-[18px] lg:text-[24px] lg:leading-[22px] ${
            highlighted ? "text-[#7160E8]" : "text-[#1E1E1E]"
          }`}
        >
          {title}
        </Body4>
      </div>

      {/* Description */}
      {description && (
        <div className="ml-9">
          <Body4 className="text-[16px] font-medium leading-[14px] text-[#1E1E1E] lg:text-[18px] lg:leading-[16px]">
            {description}
          </Body4>
        </div>
      )}
    </div>
  );
}

export default function ReconExtensionLandingPage() {
  return (
    <div>
      <PromoNavbar />
      <div className="main-container w-full overflow-x-hidden">
        <LPGradientBackground />
        <main className="relative z-10">
          {/* Hero Section - First Screen */}
          <section className="mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center gap-8 pt-[80px] lg:w-4/5 lg:pt-[180px]">
            {/* Logo */}
            <div className="flex h-[170px] w-[158px] items-center justify-center lg:size-[226px]">
              <ReconUpgradeLogo />
            </div>

            {/* Title */}
            <div className="flex flex-col items-center gap-4">
              <H1
                className="text-center text-[56px] font-black uppercase leading-[50px] tracking-normal lg:text-[76px] lg:leading-[68px]"
                style={{
                  background:
                    "linear-gradient(-39deg, #5100FF 0%, #FFFFFF 33%)",
                  WebkitBackgroundClip: "text",
                  WebkitTextFillColor: "transparent",
                  backgroundClip: "text",
                }}
              >
                One click
                <br />
                invariant testing
              </H1>

              {/* Subtitle and Buttons */}
              <div className="flex flex-col items-center gap-10">
                <Body4
                  className="max-w-[633px] text-center text-[24px] font-medium leading-[22px] lg:text-[28px] lg:leading-[25px]"
                  color="primary"
                  style={{ color: "#F2F2F2" }}
                >
                  Trusted by teams at Centrifuge, Credit Coop, and more. Over
                  250 downloads. Run Echidna and Medusa directly from VS Code.
                </Body4>

                {/* Download Buttons */}
                <div className="flex flex-col items-center gap-6">
                  <div className="flex flex-col gap-4 lg:flex-row lg:gap-4">
                    <Link
                      href="https://marketplace.visualstudio.com/items?itemName=Recon-Fuzz.recon"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <AppButton
                        variant="primary"
                        className="min-w-[200px] rounded bg-[#7160E8] p-4 text-[22px] font-bold leading-[24px] text-white"
                      >
                        Download for VS Code
                      </AppButton>
                    </Link>
                    <Link
                      href="https://marketplace.visualstudio.com/items?itemName=Recon-Fuzz.recon"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <AppButton
                        variant="secondary"
                        className="min-w-[200px] rounded border border-[#EEECEA] bg-[#D0BCFF] p-4 text-[22px] font-bold leading-[24px] text-[#1E1E1E]"
                      >
                        Download for Cursor
                      </AppButton>
                    </Link>
                  </div>

                  <Link
                    href="https://github.com/Recon-Fuzz/recon-extension"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-[22px] font-medium leading-[20px] text-[#D0BCFF] hover:underline"
                  >
                    View Source on Github
                  </Link>
                </div>
              </div>
            </div>

            <CompanyLogos />
          </section>

          {/* Features Section - Second Screen */}
          <section className="mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center gap-[72px] pt-[30px] lg:w-4/5 lg:flex-row lg:pt-[180px]">
            {/* Left Side - Video/Demo */}
            <div className="flex w-full flex-col gap-10 lg:w-[800px]">
              <div className="flex flex-col gap-1">
                <H1
                  className="text-center text-[48px] font-black uppercase leading-[43px] lg:text-left lg:text-[72px] lg:leading-[65px]"
                  style={{
                    background:
                      "linear-gradient(-28deg, #5100FF 0%, #FFFFFF 28%)",
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                    backgroundClip: "text",
                  }}
                >
                  Explanatory Features
                </H1>
              </div>

              {/* Video/Demo Placeholder */}
              <div className="relative h-[207px] w-full rounded-2xl bg-gradient-to-br from-[#F986E5] to-[#9A35F4] lg:h-[508px]">
                {/* Play Button */}
                <div className="absolute left-1/2 top-1/2 flex size-[20px] -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full bg-gradient-to-br from-white to-[#AB97FE] lg:size-[40px]">
                  <div className="flex size-[16px] items-center justify-center rounded-full bg-gradient-to-br from-[#425AF2] to-[#F5C1EC] lg:size-[32px]">
                    <div
                      className="size-[10px] bg-white lg:size-[20px]"
                      style={{ clipPath: "polygon(0 0, 100% 50%, 0 100%)" }}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Right Side - Features List */}
            <div className="flex w-full flex-col items-center gap-5 lg:w-auto lg:items-start">
              <div
                className="flex w-full flex-col gap-5 rounded-2xl p-6 backdrop-blur-[70px] lg:w-[424px]"
                style={{
                  background:
                    "linear-gradient(181deg, rgba(216, 199, 255, 1) 0%, rgba(216, 199, 255, 0) 100%)",
                  border: "2px solid transparent",
                  backgroundImage:
                    "linear-gradient(146deg, rgba(255, 255, 255, 1) 3%, rgba(255, 255, 255, 0) 57%, rgba(255, 255, 255, 0.77) 100%)",
                  backgroundClip: "border-box",
                }}
              >
                {/* Feature Items */}
                <FeatureItem title="One click Setup" />
                <FeatureItem
                  title="Integrated fuzzing"
                  description="Run Echidna and Medusa directly from VS Code"
                  highlighted
                />
                <FeatureItem title="Contract explorer" />
                <FeatureItem title="Coverage visualization" />
                <FeatureItem title="Test generation" />
                <FeatureItem title="Link Libraries (Experimental)" />
              </div>
            </div>
          </section>

          <Testimonials />
          <BenefitsExtension />
        </main>
        <div className="w-full border-t border-t-[#FFFFFF]">
          <Footer />
        </div>
      </div>
    </div>
  );
}
