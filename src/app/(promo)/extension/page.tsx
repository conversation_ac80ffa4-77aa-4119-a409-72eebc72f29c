import { cn } from "@/lib/utils";
import type { Metadata } from "next";
import Image from "next/image";
import Link from "next/link";
import { FiCheck } from "react-icons/fi";
import Footer from "../../components/Footer/Footer";
import { AppButton } from "../../components/app-button";
import { Body4, H6, H1, H2 } from "../../components/app-typography";
import { LPGradientBackground } from "../../components/gradient-wrapper";
import { ReconUpgradeLogo } from "../../components/recon-upgrade-logo";
import {
  AuditsSection,
  ReconProSection,
  ServicesSection,
  TeamSection,
  TrophiesSection,
} from "../lp-components";
import PromoNavbar from "../lp-components/PromoNavbar";
import Testimonials from "../lp-components/Testimonials/Testimonials";
import { LPSectionTitle } from "../lp-components/lp-section-title";
import extensionImg from "./extension-image.png";

export const metadata: Metadata = {
  title: "Recon Extension - One Click Invariant Testing",
  description:
    "Scaffold and run Invariant Tests with Echidna, Medusa and Halmos, directly from VS Code. Trusted by teams at Centrifuge, Credit Coop, and more.",
};

interface FeatureItemProps {
  title: string;
  description?: string;
  highlighted?: boolean;
}

function FeatureItem({
  title,
  description,
  highlighted = false,
}: FeatureItemProps) {
  return (
    <div className="border-bottom border-bottom-w-[1px] border-bottom-color-accent-alt-primary">
      <div className="flex items-center gap-3">
        <div className="flex size-6 items-center justify-center rounded-full bg-[#7160E8]">
          <FiCheck className="text-white" size={12} />
        </div>

        <H6
          color={highlighted ? "primary" : "secondary"}
          className={cn(
            {
              "mb-0": !!description,
              "mb-4": !description,
            },
            "uppercase"
          )}
        >
          {title}
        </H6>
      </div>

      {description && (
        <Body4 className="mb-[20px] mt-[12px] text-[16px] font-medium leading-[14px] text-accent-secondary lg:text-[18px] lg:leading-[16px]">
          {description}
        </Body4>
      )}
    </div>
  );
}

export default function ReconExtensionLandingPage() {
  return (
    <div>
      <PromoNavbar />
      <div className="main-container w-full overflow-x-hidden">
        <LPGradientBackground />
        <main className="relative z-10">
          <section className="mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center gap-8 pt-[80px] lg:w-4/5 lg:pt-[180px]">
            <div className="flex h-[170px] w-[158px] items-center justify-center lg:size-[270px]">
              <ReconUpgradeLogo width={225} height={250} />
            </div>

            <div className="flex flex-col items-center gap-4">
              <LPSectionTitle className="text-center">
                One click
                <br />
                invariant testing
              </LPSectionTitle>

              <div className="flex flex-col items-center gap-10">
                <H6
                  className="max-w-[633px] text-center "
                  color="primary"
                  style={{ color: "#F2F2F2" }}
                >
                  Trusted by teams at Centrifuge, Credit Coop, and more. Over
                  <span className="text-accent-primary">250 downloads.</span>
                  Run Echidna and Medusa directly from VS Code.
                </H6>

                <div className="flex flex-col items-center gap-6">
                  <div className="flex flex-col gap-4 lg:flex-row lg:gap-4">
                    <Link
                      href="https://marketplace.visualstudio.com/items?itemName=Recon-Fuzz.recon"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <AppButton variant="primary" size="lg">
                        Download for VS Code
                      </AppButton>
                    </Link>
                    <Link
                      href="https://marketplace.visualstudio.com/items?itemName=Recon-Fuzz.recon"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <AppButton variant="secondary" size="lg">
                        Download for Cursor
                      </AppButton>
                    </Link>
                  </div>

                  <Link
                    href="https://github.com/Recon-Fuzz/recon-extension"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-[22px] font-medium leading-[20px] text-[#D0BCFF] hover:underline"
                  >
                    View Source on Github
                  </Link>
                </div>
              </div>
            </div>
          </section>

          <section className="">
            <LPSectionTitle> Explanatory Features</LPSectionTitle>

            <div className="flex gap-[70px]">
              <Image
                src={extensionImg}
                alt="extension image"
                width={800}
                height={500}
              />

              <div>
                <FeatureItem title="One click Setup" />
                <FeatureItem
                  title="Integrated fuzzing"
                  description="Run Echidna and Medusa directly from VS Code"
                  highlighted
                />
                <FeatureItem title="Contract explorer" />
                <FeatureItem title="Coverage visualization" />
                <FeatureItem title="Test generation" />
                <FeatureItem title="Link Libraries (Experimental)" />
              </div>
            </div>
          </section>

          <Testimonials />

          <TrophiesSection />

          <AuditsSection />

          <TeamSection />

          <ServicesSection />

          <ReconProSection />
        </main>
        <div className="w-full border-t border-t-[#FFFFFF]">
          <Footer />
        </div>
      </div>
    </div>
  );
}
