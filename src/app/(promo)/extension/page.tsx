import { cn } from "@/lib/utils";
import type { Metadata } from "next";
import Image from "next/image";
import Link from "next/link";
import { FiCheck } from "react-icons/fi";
import Footer from "../../components/Footer/Footer";
import { AppButton } from "../../components/app-button";
import { Body4, H6, H1, H2 } from "../../components/app-typography";
import { LPGradientBackground } from "../../components/gradient-wrapper";
import { ReconUpgradeLogo } from "../../components/recon-upgrade-logo";
import {
  AuditsSection,
  ReconProSection,
  ServicesSection,
  TeamSection,
  TrophiesSection,
} from "../lp-components";
import PromoNavbar from "../lp-components/PromoNavbar";
import Testimonials from "../lp-components/Testimonials/Testimonials";
import { LPSectionTitle } from "../lp-components/lp-section-title";
import extensionImg from "./extension-image.png";

export const metadata: Metadata = {
  title: "Recon Extension - One Click Invariant Testing",
  description:
    "Scaffold and run Invariant Tests with Echidna, Medusa and Halmos, directly from VS Code. Trusted by teams at Centrifuge, Credit Coop, and more.",
};

interface FeatureItemProps {
  title: string;
  description?: string;
  highlighted?: boolean;
}

function FeatureItem({
  title,
  description,
  highlighted = false,
}: FeatureItemProps) {
  return (
    <div className="border-b-accent-alt-primary/70 flex flex-col gap-5 border-b pb-5">
      <div className="flex items-center gap-3">
        <div className="flex size-6 items-center justify-center rounded-full bg-accent-primary">
          <FiCheck className="text-white" size={12} />
        </div>

        <Body4
          className={cn(
            "text-[28px] font-black leading-[41px] tracking-[0.37px]",
            highlighted
              ? "text-accent-alt-primary"
              : "text-fore-neutral-primary opacity-90"
          )}
        >
          {title}
        </Body4>
      </div>

      {description && (
        <div className="ml-9">
          <Body4 className="text-[20px] font-bold leading-[20px] tracking-[-0.24px] text-fore-neutral-secondary">
            {description}
          </Body4>
        </div>
      )}
    </div>
  );
}

export default function ReconExtensionLandingPage() {
  return (
    <div>
      <PromoNavbar />
      <div className="main-container w-full overflow-x-hidden">
        <LPGradientBackground />
        <main className="relative z-10">
          <section className="mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center gap-8 pt-[80px] lg:w-4/5 lg:pt-[180px]">
            <div className="flex h-[170px] w-[158px] items-center justify-center lg:size-[270px]">
              <ReconUpgradeLogo width={225} height={250} />
            </div>

            <div className="flex flex-col items-center gap-4">
              <LPSectionTitle className="text-center">
                One click
                <br />
                invariant testing
              </LPSectionTitle>

              <div className="flex flex-col items-center gap-10">
                <H6
                  className="max-w-[633px] text-center "
                  color="primary"
                  style={{ color: "#F2F2F2" }}
                >
                  Trusted by teams at Centrifuge, Credit Coop, and more. Over
                  <span className="text-accent-primary">250 downloads.</span>
                  Run Echidna and Medusa directly from VS Code.
                </H6>

                <div className="flex flex-col items-center gap-6">
                  <div className="flex flex-col gap-4 lg:flex-row lg:gap-4">
                    <Link
                      href="https://marketplace.visualstudio.com/items?itemName=Recon-Fuzz.recon"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <AppButton variant="primary" size="lg">
                        Download for VS Code
                      </AppButton>
                    </Link>
                    <Link
                      href="https://marketplace.visualstudio.com/items?itemName=Recon-Fuzz.recon"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <AppButton variant="secondary" size="lg">
                        Download for Cursor
                      </AppButton>
                    </Link>
                  </div>

                  <Link
                    href="https://github.com/Recon-Fuzz/recon-extension"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-[22px] font-medium leading-[20px] text-[#D0BCFF] hover:underline"
                  >
                    View Source on Github
                  </Link>
                </div>
              </div>
            </div>
          </section>

          {/* Features Section - Second Screen */}
          <section className="mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center gap-[72px] pt-[30px] lg:w-4/5 lg:flex-row lg:pt-[180px]">
            {/* Left Side - Video/Demo */}
            <div className="flex w-full flex-col gap-10 lg:w-[800px]">
              <div className="flex flex-col gap-1">
                <H1
                  className="text-center text-[48px] font-black uppercase leading-[43px] lg:text-left lg:text-[72px] lg:leading-[65px]"
                  style={{
                    background:
                      "linear-gradient(-28deg, var(--fill-accent-primary) 0%, var(--fore-neutral-primary) 28%)",
                    WebkitBackgroundClip: "text",
                    WebkitTextFillColor: "transparent",
                    backgroundClip: "text",
                  }}
                >
                  Explanatory Features
                </H1>
              </div>

              {/* Video/Demo Placeholder */}
              <div className="relative h-[207px] w-full rounded-2xl bg-gradient-to-br from-accent-alt-secondary to-accent-primary lg:h-[508px]">
                {/* Play Button */}
                <div className="absolute left-1/2 top-1/2 flex size-[20px] -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full bg-gradient-to-br from-fore-neutral-primary to-accent-alt-primary lg:size-[40px]">
                  <div className="flex size-[16px] items-center justify-center rounded-full bg-gradient-to-br from-accent-primary to-accent-alt-secondary lg:size-[32px]">
                    <div
                      className="size-[10px] bg-fore-neutral-primary lg:size-[20px]"
                      style={{ clipPath: "polygon(0 0, 100% 50%, 0 100%)" }}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Right Side - Features List */}
            <div className="flex w-full flex-col items-center gap-5 lg:w-auto lg:items-start">
              <div
                className="flex w-full flex-col gap-5 rounded-2xl p-6 backdrop-blur-[70px] lg:w-[424px]"
                style={{
                  background:
                    "linear-gradient(181deg, var(--fill-accent-alt-primary) 0%, transparent 100%)",
                  border: "2px solid transparent",
                  backgroundImage:
                    "linear-gradient(146deg, var(--fore-neutral-primary) 3%, transparent 57%, var(--fore-neutral-primary) 100%)",
                  backgroundClip: "border-box",
                }}
              >
                {/* Feature Items */}
                <FeatureItem title="One click Setup" />
                <FeatureItem
                  title="Integrated fuzzing"
                  description="Run Echidna and Medusa directly from VS Code"
                  highlighted
                />
                <FeatureItem title="Contract explorer" />
                <FeatureItem title="Coverage visualization" />
                <FeatureItem title="Test generation" />
                <FeatureItem title="Link Libraries (Experimental)" />
              </div>
            </div>
          </section>

          <Testimonials />

          <TrophiesSection />

          <AuditsSection />

          <TeamSection />

          <ServicesSection />

          <ReconProSection />
        </main>
        <div className="w-full border-t border-t-[#FFFFFF]">
          <Footer />
        </div>
      </div>
    </div>
  );
}
