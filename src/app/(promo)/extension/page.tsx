"use client";

import Link from "next/link";
import {
  AppVideoCarousel
} from "../../components/AppVideosCarousel/AppVideosCarousel";

const TUTORIAL_VIDEOS = [
  {
    title: "The Dangers of Arbitrary Calls and How to do them safely",
    url: "https://www.youtube.com/watch?v=8-qWL2Dcgpc",
    duration: "42min",
    thumbnail: "https://img.youtube.com/vi/8-qWL2Dcgpc/maxresdefault.jpg",
  },
  {
    title:
      "Eigenlayer Ecosystem Fuzzing - Write Invariant Tests for Eigenlayer in just a few minutes",
    url: "https://www.youtube.com/watch?v=WDdFVZpTAZo",
    duration: "30min",
    thumbnail: "https://img.youtube.com/vi/WDdFVZpTAZo/maxresdefault.jpg",
  },
  {
    title: "The Recon Pro Workflow",
    url: "https://www.youtube.com/watch?v=DHAvBrsITRU",
    duration: "44min",
    thumbnail: "https://img.youtube.com/vi/DHAvBrsITRU/maxresdefault.jpg",
  },
];

import Footer from "../../components/Footer/Footer";
import { AppButton } from "../../components/app-button";
import { LPGradientBackground } from "../../components/gradient-wrapper"; 
import PromoNavbar from "../lp-components/PromoNavbar";
import Testimonials from "../lp-components/Testimonials/Testimonials";
import CompanyLogos from "../lp-components/company-logos";
import { LPSectionTitle } from "../lp-components/lp-section-title";
import BenefitsExtension from "../lp-components/BenefitsExtension/BenefitsExtension";

export default function ReconExtensionLandingPage() {
  return (
    <div>
      <PromoNavbar />
      <div className="main-container w-full overflow-x-hidden">
        <LPGradientBackground />
        <main className="relative z-10">
          <section className="mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center pt-[80px] lg:w-4/5 lg:pt-[180px]">
            <h1 className="main-title-custom mb-5 text-center text-[56px] font-bold leading-[48px] tracking-normal lg:text-[120px] lg:leading-[100px]">
              One Click Invariant Testing, directly in your IDE
            </h1>
            <h2 className="mb-5 text-center font-thin tracking-normal text-white lg:my-[37px] lg:text-[37px] lg:leading-[33px]">
              Scaffold and run Invariant Tests with Echidna, Medusa and Halmos, instantly debug with Foundry.
            </h2>
            <Link
              href="https://marketplace.visualstudio.com/items?itemName=Recon-Fuzz.recon"
              className="m-0 flex flex-row items-center justify-center p-0 text-center"
              target="_blank"
              rel="noopener noreferrer"
            >
              <AppButton variant="primary" size="lg">
                Install the Recon Extension
              </AppButton>
            </Link>

            <CompanyLogos />
          </section>

          <section
            id="benefits"
            className="mx-auto mb-5 flex w-[87.5%] flex-col items-center justify-center pt-[30px] lg:pt-[180px]"
          >
            <h3 className=" sub-title-custom text-center font-bold uppercase leading-[48px] tracking-normal lg:text-[100px] lg:leading-[100px]">
              Benefits
            </h3>
            <BenefitsExtension />
          </section>

          <section
            id="demos"
            className="mx-auto mb-5 mt-[30px] flex w-full flex-col items-center justify-center bg-[#6750A41F] pt-[30px] lg:p-[30px]"
          >
            <LPSectionTitle className="mb-[30px]">
              Tutorial Videos
            </LPSectionTitle>
            <AppVideoCarousel videos={TUTORIAL_VIDEOS} />
          </section>

          <Testimonials />

        </main>
        <div className="w-full border-t border-t-[#FFFFFF]">
          <Footer />
        </div>
      </div>
    </div>
  );
}
