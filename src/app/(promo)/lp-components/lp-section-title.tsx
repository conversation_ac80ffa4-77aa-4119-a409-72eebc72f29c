import { cn } from "@/lib/utils";
import type { ReactNode } from "react";

export const LPSectionTitle = ({
  children,
  className = "",
}: {
  children: ReactNode;
  className?: string;
}) => {
  return (
    <h3
      className={cn(
        "sub-title-custom items-start font-bold uppercase leading-[48px] tracking-normal lg:text-[100px] lg:leading-[100px]",
        className
      )}
      style={{
        background: "linear-gradient(-39deg, #5100FF 0%, #FFFFFF 33%)",
        WebkitBackgroundClip: "text",
        WebkitTextFillColor: "transparent",
        backgroundClip: "text",
      }}
    >
      {children}
    </h3>
  );
};
