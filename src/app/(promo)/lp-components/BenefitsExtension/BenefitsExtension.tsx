const BenefitsArray = [
  {
    title: "FOUNDRY FIRST",
    description:
      "Our Extension integrates automatically with any Foundry Project",
    upcoming: false,
    experimental: false,
  },
  {
    title: "FASTER THAN ANYTHING ELSE",
    description:
      "We've been running invariant tests every day for over 2 years, we've optimized for speed of iteration",
    upcoming: false,
    experimental: false,
  },
  {
    title: "PROVEN IMPACT",
    description:
      "In 2025 we've used the extension to prevent Billions in damages thoroughout our audits, and we've prevented $20 MLN in damages on Deployed Smart Contracts ",
    upcoming: false,
    experimental: false,
  },
  {
    title: "ONCE CLICK SCAFFOLDING",
    description: "Instantly scaffold an Invariant Testing suite that works with Echidna, Medusa, Halmos and Foundry",
    upcoming: false,
    experimental: false,
  },
  {
    title: "ONE CLICK MOCKS",
    description:
      "Right click any contract, generate a mock",
    upcoming: false,
    experimental: false,
  },
  {
    title: "AUTOMATIC REPORT GENERATION",
    description:
      "Run any tool, generate a report and repros you can easily debug in foundry.",
    upcoming: false,
    experimental: false,
  },
];

interface BenefitProps {
  title: string;
  description: string;
  upcoming?: boolean;
  experimental?: boolean;
}
function BenefitExtension({ title, description, upcoming, experimental }: BenefitProps) {
  return (
    <div className="relative mx-auto flex h-auto w-full max-w-[448px] flex-col items-center justify-start overflow-hidden rounded-lg bg-[rgba(103,80,164,0.12)] p-5 pt-10 text-center">
      {upcoming && (
        <span className="absolute right-0 top-0 flex h-[36px] w-[176px] items-center justify-center rounded-bl-lg rounded-tr-lg bg-gradient-to-r from-[#EA5A4E] to-[#ED93D3] text-sm font-semibold text-white shadow-md">
          Upcoming Feature
        </span>
      )}
      <div className="flex size-full flex-col justify-between">
        <div className="flex w-full flex-col">
          <h2 className="w-full overflow-hidden break-words text-[24px] font-bold capitalize leading-none text-white md:text-[32px] lg:text-[52px]">
            {title}
          </h2>
        </div>
        <p className="mt-4 break-words text-[14px] text-white lg:text-[18px]">
          {description}
        </p>
        {experimental && (
          <p className="text-center text-[10px] font-light uppercase tracking-wider text-white lg:text-[14px]">
            Experimental
          </p>
        )}
      </div>
    </div>
  );
}

export default function BenefitsExtension() {
  return (
    <div className="flex items-center justify-center px-4 py-16">
      <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
        {BenefitsArray.map((benefit, index) => {
          return (
            <BenefitExtension
              key={index}
              title={benefit.title}
              description={benefit.description}
              upcoming={benefit.upcoming}
              experimental={benefit.experimental}
            />
          );
        })}
      </div>
    </div>
  );
}
