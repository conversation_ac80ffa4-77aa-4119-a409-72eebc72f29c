import Link from "next/link";

import { AppButton } from "../../../../components/app-button";
import { Body3, H6 } from "../../../../components/app-typography";
import { ReconUpgradeLogo } from "../../../../components/recon-upgrade-logo";

export const UpgradeToPro = () => {
  return (
    <div className="mx-[18px] mb-[20px] flex w-[268px] flex-col items-center justify-center gap-[10px] self-center rounded-[12px] border border-stroke-neutral-decorative bg-accent-alt-primary px-[12px] pb-[16px] pt-[10px]">
      <div className="relative h-[170px] w-[152px]">
        <ReconUpgradeLogo />
      </div>

      <div className="flex w-full flex-col items-stretch">
        <H6 color="primary" className="mb-0 text-center uppercase">
          Upgrade to Pro
        </H6>

        <Body3 color="secondary" className="text-center opacity-80">
          Build both public and private repos and much more
        </Body3>
      </div>

      <Link href="/dashboard/pro" className="w-full">
        <AppButton
          variant="primary"
          size="lg"
          fullWidth
          className=" px-[12px] py-[8px]"
        >
          Go pro now
        </AppButton>
      </Link>
    </div>
  );
};
