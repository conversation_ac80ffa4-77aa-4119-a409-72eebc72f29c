"use client";
import { useEffect, useMemo, useState } from "react";
import { FaSortUp, FaSortDown } from "react-icons/fa";

import { AppButton } from "@/app/components/app-button";
import { AppInput } from "@/app/components/app-input";
import { AppSelect } from "@/app/components/app-select";
import { AppSpinner } from "@/app/components/app-spinner";
import { Body1, Body2, Body3, Heading } from "@/app/components/app-typography";

import type { Job } from "@/app/services/jobs.hooks";
import { useGetJobs } from "@/app/services/jobs.hooks";
import type { BrokenPropShow } from "@/app/types/types";
import { searchObject } from "@/lib/utils";
import { formatDateString } from "@/utils/format";
import axios from "axios";
import { mockJobs } from "@/app/mocks";
import Link from "next/link";

// Component for job status and broken properties
const JobStatusSection = ({
  job,
  index,
  showBrokenPropDropdown,
  onToggleBrokenProps,
}: {
  job: Job;
  index: number;
  showBrokenPropDropdown: BrokenPropShow[];
  onToggleBrokenProps: (index: number) => void;
}) => (
  <div className="space-y-3">
    {job.testsFailed != null && job.testsCoverage != null && (
      <div className="flex items-center gap-6 rounded-lg border border-stroke-neutral-decorative bg-back-neutral-secondary p-3">
        <div className="flex items-center gap-2">
          <span className="text-lg">
            {job.brokenProperties && job.brokenProperties.length > 0
              ? "🔴"
              : job.testsFailed == 0
                ? "🟢"
                : "🔴"}
          </span>
          <div>
            <Body3 color="tertiary" className="text-xs">
              Tests Failed
            </Body3>
            <Body3 color="primary" className="font-medium">
              {job.testsFailed}
            </Body3>
          </div>
        </div>

        <div className="h-6 w-px bg-stroke-neutral-decorative" />

        <div>
          <Body3 color="tertiary" className="text-xs">
            Coverage
          </Body3>
          <Body3 color="primary" className="font-medium">
            {job.testsCoverage}%
          </Body3>
        </div>

        {job.progress != null && (
          <>
            <div className="h-6 w-px bg-stroke-neutral-decorative" />
            <div>
              <Body3 color="tertiary" className="text-xs">
                Progress
              </Body3>
              <Body3 color="primary" className="font-medium">
                {job.progress}%
              </Body3>
            </div>
          </>
        )}

        {job.eta && job.progress < 100 && (
          <>
            <div className="h-6 w-px bg-stroke-neutral-decorative" />
            <div>
              <Body3 color="tertiary" className="text-xs">
                ETA
              </Body3>
              <Body3 color="primary" className="font-medium">
                {job.eta !== "0m" ? job.eta : "<1m"}
              </Body3>
            </div>
          </>
        )}
      </div>
    )}

    {showBrokenPropDropdown.length > 0 &&
      job.brokenProperties &&
      job.brokenProperties.length > 0 && (
        <div className="space-y-2">
          <AppButton
            variant="outline"
            size="xs"
            onClick={() => onToggleBrokenProps(index)}
            className="text-xs"
          >
            {showBrokenPropDropdown.find((el) => el.id === index)?.show
              ? "Hide"
              : "Show"}{" "}
            Broken Properties ({job.brokenProperties.length})
          </AppButton>
          {showBrokenPropDropdown.find((el) => el.id === index)?.show && (
            <div className="border-accent-primary/20 border-l-2 pl-4">
              <AppSelect
                options={job.brokenProperties.map((prop) => ({
                  value: prop.brokenProperty,
                  label: prop.brokenProperty,
                }))}
                placeholder="Select broken property"
                containerClassName="w-auto min-w-[200px]"
              />
            </div>
          )}
        </div>
      )}
  </div>
);

const JobStatusBadge = ({ status }: { status: string }) => {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case "SUCCESS":
        return {
          color: "text-status-success",
          bg: "bg-status-success/10",
          border: "border-status-success/20",
          icon: "🟢",
        };
      case "ERROR":
        return {
          color: "text-status-error",
          bg: "bg-status-error/10",
          border: "border-status-error/20",
          icon: "🔴",
        };
      case "RUNNING":
      case "STARTED":
        return {
          color: "text-accent-primary",
          bg: "bg-accent-primary/10",
          border: "border-accent-primary/20",
          icon: "🔄",
        };
      case "QUEUED":
        return {
          color: "text-fore-neutral-secondary",
          bg: "bg-back-neutral-secondary",
          border: "border-stroke-neutral-decorative",
          icon: "⏳",
        };
      case "STOPPED":
        return {
          color: "text-fore-neutral-tertiary",
          bg: "bg-back-neutral-secondary",
          border: "border-stroke-neutral-decorative",
          icon: "⏹️",
        };
      default:
        return {
          color: "text-fore-neutral-secondary",
          bg: "bg-back-neutral-secondary",
          border: "border-stroke-neutral-decorative",
          icon: "❓",
        };
    }
  };

  const config = getStatusConfig(status);

  return (
    <div
      className={`inline-flex items-center gap-2 rounded-full border px-3 py-1.5 ${config.bg} ${config.border}`}
    >
      <span className="text-sm">{config.icon}</span>
      <Body3 className={`font-medium ${config.color}`}>{status}</Body3>
    </div>
  );
};

const JobCard = ({
  job,
  index,
  isEditing,
  existingLabel,
  showBrokenPropDropdown,
  onToggleEdit,
  onLabelChange,
  onKeyPress,
  onToggleBrokenProps,
}: {
  job: Job;
  index: number;
  isEditing: boolean;
  existingLabel: string;
  showBrokenPropDropdown: BrokenPropShow[];
  onToggleEdit: () => void;
  onLabelChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onKeyPress: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  onToggleBrokenProps: (index: number) => void;
}) => (
  <div className="hover:border-accent-primary/30 group overflow-hidden rounded-xl border border-stroke-neutral-decorative bg-back-neutral-tertiary shadow-sm transition-all duration-300 hover:shadow-lg">
    <div className="p-6 pb-4">
      <div className="mb-4 flex items-start justify-between gap-4">
        <div className="min-w-0 flex-1">
          <div className="mb-2 flex items-center gap-3">
            <JobStatusBadge status={job.status} />
            <EditableJobLabel
              job={job}
              isEditing={isEditing}
              existingLabel={existingLabel}
              onToggleEdit={onToggleEdit}
              onLabelChange={onLabelChange}
              onKeyPress={onKeyPress}
            />
          </div>

          <div className="mb-3 flex items-center gap-2">
            <Body2 color="primary" className="font-medium">
              {job.orgName}/{job.repoName}
            </Body2>
            <Body3 color="secondary">•</Body3>
            <Body3 color="secondary">{job.ref}</Body3>
            <Body3 color="secondary">•</Body3>
            <Body3 color="secondary">{job.fuzzer}</Body3>
          </div>

          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-1">
              <Body3 color="tertiary">Created:</Body3>
              <Body3 color="secondary">{formatDateString(job.createdAt)}</Body3>
            </div>
            <div className="flex items-center gap-1">
              <Body3 color="tertiary">Updated:</Body3>
              <Body3 color="secondary">{formatDateString(job.updatedAt)}</Body3>
            </div>
          </div>
        </div>

        <div className="flex shrink-0 items-center gap-2">
          <Link href={`/dashboard/jobs/${job.id}`}>
            <AppButton
              variant="outline"
              size="sm"
              className="opacity-80 transition-opacity group-hover:opacity-100"
            >
              View Details
            </AppButton>
          </Link>
          {(job.status === "SUCCESS" ||
            job.status === "ERROR" ||
            job.status === "STOPPED") && (
            <Link href={`/dashboard/jobs/${job.id}/report`}>
              <AppButton
                variant="secondary"
                size="sm"
                className="opacity-80 transition-opacity group-hover:opacity-100"
              >
                View Report
              </AppButton>
            </Link>
          )}
        </div>
      </div>

      <JobStatusSection
        job={job}
        index={index}
        showBrokenPropDropdown={showBrokenPropDropdown}
        onToggleBrokenProps={onToggleBrokenProps}
      />
    </div>
  </div>
);

const SortToggle = ({
  sortDirection,
  onToggle,
}: {
  sortDirection: "asc" | "desc";
  onToggle: () => void;
}) => (
  <AppButton
    variant="outline"
    size="sm"
    onClick={onToggle}
    rightIcon={sortDirection === "asc" ? <FaSortUp /> : <FaSortDown />}
    className="min-w-fit"
  >
    <Body3 color="secondary">
      {sortDirection === "asc" ? "Oldest first" : "Newest first"}
    </Body3>
  </AppButton>
);

const FilterToggle = ({
  filterNoBroken,
  onToggle,
}: {
  filterNoBroken: boolean;
  onToggle: () => void;
}) => (
  <AppButton
    variant={filterNoBroken ? "secondary" : "outline"}
    size="sm"
    onClick={onToggle}
    className="min-w-fit"
  >
    <Body3 color={filterNoBroken ? "primary" : "secondary"}>
      {filterNoBroken ? "With broken props" : "All jobs"}
    </Body3>
  </AppButton>
);

const SortBySelector = ({
  sortBy,
  onSortByChange,
}: {
  sortBy: string;
  onSortByChange: (value: string) => void;
}) => {
  const sortOptions = [
    { value: "createdAt", label: "Created Date" },
    { value: "updatedAt", label: "Updated Date" },
    { value: "status", label: "Status" },
    { value: "fuzzer", label: "Fuzzer" },
  ];

  return (
    <div className="flex flex-col gap-1">
      <Body3 color="secondary" className="text-xs">
        Sort by
      </Body3>
      <AppSelect
        value={sortBy}
        onChange={(e) => onSortByChange(e.target.value)}
        options={sortOptions}
        containerClassName="w-[260px]"
      />
    </div>
  );
};

const EditableJobLabel = ({
  job,
  isEditing,
  existingLabel,
  onToggleEdit,
  onLabelChange,
  onKeyPress,
}: {
  job: Job;
  isEditing: boolean;
  existingLabel: string;
  onToggleEdit: () => void;
  onLabelChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onKeyPress: (e: React.KeyboardEvent<HTMLInputElement>) => void;
}) => (
  <div
    onDoubleClick={onToggleEdit}
    className="group/label cursor-pointer rounded-lg border border-transparent px-3 py-2 transition-all duration-200 hover:border-stroke-neutral-decorative hover:bg-back-neutral-secondary"
  >
    {isEditing ? (
      <input
        value={existingLabel}
        onChange={onLabelChange}
        onKeyDown={onKeyPress}
        className="h-8 w-full min-w-[120px] rounded-md border border-stroke-neutral-decorative bg-transparent px-3 text-lg font-semibold text-fore-neutral-primary outline-none transition-all duration-200 placeholder:text-fore-neutral-quaternary hover:border-fore-neutral-secondary focus:border-accent-primary"
        placeholder="Job label"
        autoFocus
      />
    ) : (
      <div className="flex items-center gap-2">
        <Body1 color="primary" className="text-lg font-semibold">
          {job.label || `Job ${job.id.slice(0, 8)}`}
        </Body1>
        <Body3
          color="tertiary"
          className="text-xs opacity-0 transition-opacity group-hover/label:opacity-100"
        >
          Double-click to edit
        </Body3>
      </div>
    )}
  </div>
);

export const AllJobs = () => {
  const [isEditing, setIsEditing] = useState({});
  const [existingLabels, setExistingLabels] = useState({});
  const { data: apiData, isLoading, refetch, isRefetching } = useGetJobs();
  const data = apiData || mockJobs;

  const [showBrokenPropDropdown, setShowBrokenPropDropdown] = useState<
    BrokenPropShow[]
  >([]);

  const buttonDisabled = isLoading || isRefetching;

  const [sortBy, setSortBy] = useState("createdAt");
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc");

  function toggleSortDirection() {
    setSortDirection(sortDirection === "asc" ? "desc" : "asc");
  }

  const [filterNoBroken, setFilterNoBroken] = useState(false);
  function toggleFilterBroken() {
    setFilterNoBroken(!filterNoBroken);
  }

  const [query, setQuery] = useState("");

  const sortedJobs = useMemo(() => {
    return (
      data
        ?.sort((jobA, jobB) => {
          const a = new Date(jobA[sortBy]).getTime();
          const b = new Date(jobB[sortBy]).getTime();
          return sortDirection === "asc" ? a - b : b - a;
        })
        // Keywords
        .filter((job) => {
          if (!query) return true;

          const queryWords = query.toLowerCase().split(/\s+/);
          return queryWords.every((word) => searchObject(job, word));
        })
        // Filter no broken props
        .filter((job) =>
          filterNoBroken
            ? job.brokenProperties && job.brokenProperties.length > 0
            : true
        )
        .map((job) => ({
          ...job,
          status: job.brokenProperties.length > 0 ? "ERROR" : job.status,
        }))
    );
  }, [sortBy, data, query, filterNoBroken, sortDirection]);

  useEffect(() => {
    if (sortedJobs && sortedJobs.length > 0) {
      setShowBrokenPropDropdown(
        sortedJobs.map((_, index) => ({
          id: index,
          show: false,
        }))
      );
    }
  }, [sortedJobs]);

  const showBrokenPropHandler = (index: number) => {
    setShowBrokenPropDropdown((prev) =>
      prev.map((trace) => {
        if (trace.id === index) {
          return {
            ...trace,
            show: !trace.show,
          };
        }
        return trace;
      })
    );
  };

  useEffect(() => {
    if (!data) return;

    const newIsEditing = {};
    const newExistingLabels = {};

    data.forEach((job) => {
      newIsEditing[job.id] = false;
      newExistingLabels[job.id] = job.label ? job.label : "";
    });

    setIsEditing(newIsEditing);
    setExistingLabels(newExistingLabels);
  }, [data]);

  const updateJob = async (id: string) => {
    const res = await axios({
      method: "POST",
      url: `/api/jobs/updateLabel`,
      data: {
        newLabel: existingLabels[id],
        jobId: id,
      },
    });
    if (res.data.data === "success") {
      refetch();
    }
  };

  const newLabelHandling = (
    id: string,
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    setExistingLabels({
      ...existingLabels,
      [id]: e.target.value,
    });
  };

  const handleKeyPress = (
    id: string,
    event: React.KeyboardEvent<HTMLInputElement>
  ) => {
    if (event.key === "Enter") {
      updateJob(id);
    }
  };

  return (
    <div className="mb-5 mt-12">
      <div className="mb-8">
        <div className="mb-6 flex items-center justify-between">
          <Heading color="primary">All Jobs</Heading>
          <AppButton
            onClick={refetch}
            disabled={buttonDisabled}
            variant="outline"
            size="sm"
          >
            {buttonDisabled ? <AppSpinner /> : "Refresh"}
          </AppButton>
        </div>

        <div className="flex flex-wrap items-end gap-4 rounded-lg border border-stroke-neutral-decorative bg-back-neutral-secondary p-4">
          <div className="flex min-w-[300px] flex-col gap-1">
            <Body3 color="secondary" className="text-xs">
              Search
            </Body3>
            <AppInput
              type="text"
              placeholder="Search names, properties, fuzzer, coverage…"
              value={query}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                setQuery(e.target.value)
              }
              className="w-full max-w-[300px]"
            />
          </div>

          <SortBySelector sortBy={sortBy} onSortByChange={setSortBy} />

          <div className="flex items-center gap-2">
            <SortToggle
              sortDirection={sortDirection}
              onToggle={toggleSortDirection}
            />
            <FilterToggle
              filterNoBroken={filterNoBroken}
              onToggle={toggleFilterBroken}
            />
          </div>

          <div className="ml-auto">
            <Body3 color="secondary">
              {sortedJobs?.length || 0} job{sortedJobs?.length !== 1 ? "s" : ""}{" "}
              found
            </Body3>
          </div>
        </div>
      </div>

      {sortedJobs?.length > 0 && (
        <div className="flex flex-col gap-5">
          {sortedJobs.map((job, index) => (
            <JobCard
              key={job.id}
              job={job}
              index={index}
              isEditing={isEditing[job.id]}
              existingLabel={existingLabels[job.id]}
              showBrokenPropDropdown={showBrokenPropDropdown}
              onToggleEdit={() =>
                setIsEditing((prevState) => ({
                  ...prevState,
                  [job.id]: !prevState[job.id],
                }))
              }
              onLabelChange={(event) => newLabelHandling(job.id, event)}
              onKeyPress={(event) => handleKeyPress(job.id, event)}
              onToggleBrokenProps={showBrokenPropHandler}
            />
          ))}
        </div>
      )}
    </div>
  );
};
