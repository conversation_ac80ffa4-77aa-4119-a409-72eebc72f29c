"use client";
import { use } from "react";
import SingleJobContainer from "@/app/components/SingleJobContainer";
import { useGetJobById } from "@/app/services/jobs.hooks";
import { useGetShareByJobId } from "@/app/services/shares.hook";
import { MainContentWrapper } from "@/app/(app)/components/main-content-wrapper";

export default function SingleJobPage({
  params,
}: {
  params: Promise<{ jobId: string }>;
}) {
  const { jobId } = use(params);
  const { data: jobData, isLoading } = useGetJobById(jobId);

  const {
    data: shareInfo,
    isLoading: isJobInfoLoading,
    refetch: reloadShares,
  } = useGetShareByJobId(jobId);

  // TODO: Make share

  // TODO: Fetch if shared

  // Get single Share -> You can just fetch all for now and check if one of them matches
  // Long term: Separate fetching all (Separate page, super expensive)
  // With fetching one, fast and simple

  return (
    <MainContentWrapper>
      <SingleJobContainer
        isJobInfoLoading={isJobInfoLoading}
        shareInfo={shareInfo}
        jobId={jobId}
        reloadShares={reloadShares}
        jobData={jobData}
        isLoading={isLoading}
      />
    </MainContentWrapper>
  );
}
