"use client";

import { AppTab<PERSON>ane, AppTabs } from "@/app/components/app-tabs";

import { ABIActionsRow } from "./abi-actions-row";
import { ABIProvider } from "./abi-context";
import { BeforeAfterContracts } from "./before-after-contracts";
import { ResultsPage } from "./results-page";
import { SelectContracts } from "./select-contracts";
import axios from "axios";
import { AppButton } from "@/app/components/app-button";
import { useGetAbiByIdentifier } from "@/app/services/abi.hook";
import { use, useState } from "react";
import { MainContentWrapper } from "@/app/(app)/components/main-content-wrapper";

type RepoPageProps = {
  params: Promise<{ identifier: string }>;
};

export default function RepoPage({ params }: RepoPageProps) {
  const { identifier } = use(params);
  const { data } = useGetAbiByIdentifier(identifier);

  // NOTE: Given ABI, filter for Path if toggled

  // PAGE - 0
  // If they toggle
  // We don't necessarily want to forget anything
  // So we are prob better off using the same idea of Contract - Selector
  // Rather than anything else

  // Abi data -> name
  // Abi data -> abiPath

  // Contract Name
  // Abi selectors

  // So we can re-use the same for both lists

  // Page 2 -  All of the Fuzzer Contracts

  // Onload, set fuzzer contracts

  const [loading, setLoading] = useState(false);

  const deleteAbiHandler = async () => {
    setLoading(true);
    try {
      await axios({
        method: "POST",
        url: `/api/abiDelete/`,
        data: {
          abiId: data.id,
        },
      });
      window.location.href = "/dashboard";
    } catch (e) {
      console.log(e);
    }
    setLoading(false);
  };

  return (
    <MainContentWrapper>
      <ABIProvider identifier={identifier}>
        <div className="pl-[45px] pr-[80px] pt-[45px]">
          <h1 className="mb-[20px] text-[28px] leading-[33px] text-fore-neutral-primary">
            Build your Handlers
          </h1>
          <div className="flex gap-[40px]">
            <AppTabs
              defaultActiveKey="select-contacts"
              className="min-w-[400px]"
            >
              <AppTabPane tab="select-contacts" label="Contracts">
                <SelectContracts />
              </AppTabPane>
              <AppTabPane
                tab="before-after-contacts"
                label="Before and after trackers"
              >
                <BeforeAfterContracts />
              </AppTabPane>
            </AppTabs>
            <div className="min-w-[500px] grow">
              <ABIActionsRow />
              <ResultsPage />
            </div>
          </div>
          <div className="mb-10 flex w-full flex-row justify-end">
            <AppButton
              variant="secondary"
              className="mb-10 p-0"
              onClick={deleteAbiHandler}
            >
              {loading ? "Deleting" : "Delete"} Abi Data
            </AppButton>
          </div>
        </div>
      </ABIProvider>
    </MainContentWrapper>
  );
}
