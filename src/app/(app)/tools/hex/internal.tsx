"use client";
import { useMemo, useState } from "react";
import Link from "next/link";
import { FiArrowLeft } from "react-icons/fi";

import { AppTextarea } from "@/app/components/app-textarea";
import { AppCode } from "@/app/components/app-code";
import { Body2, H1, H3 } from "@/app/components/app-typography";
import { GradientWrapper } from "@/app/components/gradient-wrapper";
import { AppHeader } from "@/app/(app)/components/app-header";
import { AppInputDropdown } from "@/app/components/app-input-dropdown";
import { AppInput } from "@/app/components/app-input";


type SolidityBasicType = 
  | `uint${8 | 16 | 24 | 32 | 40 | 48 | 56 | 64 | 72 | 80 | 88 | 96 | 104 | 112 | 120 | 128 | 
           136 | 144 | 152 | 160 | 168 | 176 | 184 | 192 | 200 | 208 | 216 | 224 | 232 | 240 | 248 | 256}`
  | `int${8 | 16 | 24 | 32 | 40 | 48 | 56 | 64 | 72 | 80 | 88 | 96 | 104 | 112 | 120 | 128 | 
          136 | 144 | 152 | 160 | 168 | 176 | 184 | 192 | 200 | 208 | 216 | 224 | 232 | 240 | 248 | 256}`
  | 'address' 
  | 'bool';


function makeSolidityBasicType(prefix: string, size: number): SolidityBasicType {
  if(size > 256) {
    throw new Error("Size must be less than 256");
  }
  return `${prefix}${size}` as SolidityBasicType;
}

function makeSolidityBasicTypes(prefix: string, maxSize: number): SolidityBasicType[] {
  return Array.from({ length: maxSize / 8 }, (_, i) => makeSolidityBasicType(prefix, (i + 1) * 8));
}

const SOLIDITY_BASIC_TYPES: SolidityBasicType[] = [
  ...makeSolidityBasicTypes("uint", 256),
  ...makeSolidityBasicTypes("int", 256),
  "address",
  "bool",
];

/**
 * Convert hex string to Solidity basic type human-readable value
 * Supports: uint8-256, int8-256, address, bool
 * @param {string} hexString - The hex string (with or without '0x' prefix)
 * @param {string} solidityType - The Solidity type (e.g., 'uint256', 'int128', 'address', 'bool')
 * @returns {string|bigint|boolean} - The human-readable value
 */
/**
 * Convert hex string to Solidity basic type human-readable value
 * Supports: uint8-256, int8-256, address, bool
 * @param {string} hexString - The hex string (with or without '0x' prefix)
 * @param {SolidityBasicType} solidityType - The Solidity type (e.g., 'uint256', 'int128', 'address', 'bool')
 * @returns {string|bigint|boolean} - The human-readable value
 */
function hexToSolidityType(hexString, solidityType) {
  // if hexstring is greater than 32 bytes, throw an error
  // starts with 0x, then 66 is the max length for an address
  if (hexString.startsWith('0x') && hexString.length > 66) { // 64 + 2 for the 0x prefix
    throw new Error('Hex string is too long');
  }

  if(!hexString.startsWith('0x') && hexString.length > 64) {
    throw new Error('Hex string is too long');
  }


  // Remove '0x' prefix if present
  const cleanHex = hexString.startsWith('0x') ? hexString.slice(2) : hexString;
  
  // Validate hex string
  if (!/^[0-9a-fA-F]*$/.test(cleanHex)) {
    throw new Error('Invalid hex string');
  }
  
  // Handle empty hex string - treat as 0
  const hexValue = cleanHex || '0';
  
  // Parse the type
  const type = solidityType.toLowerCase();
  
  // Handle address type
  if (type === 'address') {
    // Addresses are 20 bytes (40 hex chars)
    const padded = cleanHex.padStart(40, '0');
    return '0x' + padded.slice(-40);
  }
  
  // Handle boolean type
  if (type === 'bool') {
    const value = BigInt('0x' + hexValue);
    return value !== 0n;
  }
  
  // Handle uint types (uint8, uint16, ..., uint256)
  if (type.startsWith('uint')) {
    const bitsMatch = type.match(/^uint(\d+)$/);
    if (bitsMatch) {
      const bits = parseInt(bitsMatch[1]);
      if (bits % 8 !== 0 || bits < 8 || bits > 256) {
        throw new Error('Invalid uint type: bits must be multiple of 8, between 8 and 256');
      }
      
      const value = BigInt('0x' + hexValue);
      const maxValue = (1n << BigInt(bits)) - 1n;
      
      // Truncate to fit the type (modulo max value + 1)
      const truncated = value & maxValue;
      
      return truncated;
    }
  }
  
  // Handle int types (int8, int16, ..., int256)
  if (type.startsWith('int')) {
    const bitsMatch = type.match(/^int(\d+)$/);
    if (bitsMatch) {
      const bits = parseInt(bitsMatch[1]);
      if (bits % 8 !== 0 || bits < 8 || bits > 256) {
        throw new Error('Invalid int type: bits must be multiple of 8, between 8 and 256');
      }
      
      const value = BigInt('0x' + hexValue);
      const mask = (1n << BigInt(bits)) - 1n; // Create mask for the bit width
      const signBit = 1n << BigInt(bits - 1); // Sign bit position
      
      // Truncate to fit the type width
      const truncated = value & mask;
      
      // Check if the sign bit is set (negative number)
      if (truncated & signBit) {
        // Convert from two's complement to negative value
        // Subtract 2^bits to get the negative value
        return truncated - (1n << BigInt(bits));
      } else {
        // Positive number, return as-is
        return truncated;
      }
    }
  }
  
  throw new Error(`Unsupported Solidity type: ${solidityType}. Only basic types are supported: uint, int, address, bool`);
}


export default function HexUintToolInternal() {
  const [hex, setHex] = useState("");

  const [solidityType, setSolidityType] = useState<SolidityBasicType>("uint256");



  const humanReadableValue = useMemo(() => {
    try {
      return hexToSolidityType(hex, solidityType);
    } catch (error) {
      console.error("Error", error);
      return error.message;
    }
  }, [hex, solidityType]);

  return (
    <div className="main-container w-full overflow-x-hidden">
      <div className="relative z-10 min-h-screen">
        <AppHeader skipUser />

        <GradientWrapper className="flex min-h-[calc(100vh-80px)] items-center justify-center py-8">
          <div className="w-full max-w-4xl rounded-[20px] bg-back-neutral-tertiary px-[48px] py-[40px]">
            <div className="mb-8 text-left">
              <Link
                href="/tools"
                className="mb-6 inline-flex items-center text-fore-neutral-primary transition-colors duration-200 hover:text-fore-neutral-secondary"
              >
                <FiArrowLeft className="size-10" />
              </Link>

              <H1 className="mb-6 text-accent-primary">Hex to Solidity Type Value Converter</H1>

              <Body2 className="mb-2">
                Paste an Hex, chose a Solidity Type, and get the human-readable value
              </Body2>
              <Body2 className="mb-6">
                Supported Types: uint8-256, int8-256, address, bool
              </Body2>
            </div>

            <div className="mb-8">
              <AppInput
                className="mb-6"
                label="Hex Value"
                value={hex}
                onChange={(e) => setHex(e.target.value)}
                placeholder="Enter your hex here..."
                type="text"
              />
            </div>
            <div className="mb-8">
              <AppInputDropdown
                label="Solidity Type (Number is faster)"
                placeholder={"uint256"}
                value={solidityType}
                onItemSelect={(e) => {
                  console.log("e", e);
                  setSolidityType(e as SolidityBasicType);
                }}
                dropdownItems={SOLIDITY_BASIC_TYPES.map((type) => ({
                  id: type,
                  label: type,
                  fields: type as unknown as SolidityBasicType,
                }))}
              />
            </div>

            <div className="space-y-6">
              <div>
                <H3 className="mb-4 text-fore-neutral-primary">Decoded</H3>
                <AppCode
                  showLineNumbers={false}
                  code={humanReadableValue.toString()}
                  language="javascript"
                />
              </div>
            </div>
          </div>
        </GradientWrapper>
      </div>
    </div>
  );
}
